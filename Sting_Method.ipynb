{"cells": [{"cell_type": "code", "execution_count": null, "id": "77fa21e1", "metadata": {}, "outputs": [], "source": ["name = input(\"Enter your name: \")\n", " # length of string\n", "#result = len(name)\n", "print(name)\n", "print(len(name))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}