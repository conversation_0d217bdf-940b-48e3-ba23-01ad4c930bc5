import tensorflow as tf

fashion_mnist = tf.keras.datasets.fashion_mnist.load_data()
(X_train_full, y_train_full), (X_test, y_test) = fashion_mnist
# To split data in training and validation
X_train, y_train = X_train_full[:-5000], y_train_full[:-5000]
X_valid, y_valid = X_train_full[-5000:], y_train_full[-5000:]

import matplotlib.pyplot as plt
plt.imshow(X_train[5], cmap="binary") # cmap = color map
plt.axis('off')
plt.show()

X_train.shape #shape of trainiing data set


# Training data set type (0-255)
X_train.dtype


#pixel intensity (0-255) normalization (0-1)
X_train, X_valid, X_test = X_train / 255., X_valid / 255., X_test / 255.

X_train.dtype

class_name = ["t-shirt", "Trouser", "Pullover", "Dress", "Coat", "Sandal", "Shirt", "<PERSON>neaker", "Bag", "Ankle boot"]


class_name[y_train[5]]

pip install "tensorflow<2.11"



model = tf.keras.Sequential([tf.keras.layers.Flatten(input_shape=[28,28]),
    tf.keras.layers.Dense(300, activation="relu"),
    tf.keras.layers.Dense(100, activation="relu"),
    tf.keras.layers.Dense(10, activation="softmax")
])


model = tf.keras.Sequential()
model.add(tf.keras.layers.Flatten(input_shape=[28,28]))
model.add(tf.keras.layers.Dense(300, activation="relu"))
model.add(tf.keras.layers.Dense(100, activation="relu"))
model.add(tf.keras.layers.Dense(10, activation="softmax"))

model.summary()

model.layers

hidden1 = model.layers[1]
hidden1.name


weights, biases = hidden1.get_weights()
weights

weights.shape

biases
biases.shape

model.compile(loss="sparse_categorical_crossentropy",
              optimizer="sgd",
              metrics=["accuracy"])

history = model.fit(X_train, y_train, epochs=30,
                    validation_data=(X_valid, y_valid))

import matplotlib.pyplot as plt
import pandas as pd


tf.keras.utils.plot_model(model, to_file='model.png', show_shapes=True, show_layer_names=True)

pd.DataFrame(history.history).plot(figsize=(8, 5))
plt.grid(False)
plt.gca().set_ylim(0, 1) # set the vertical range to [0-1]
plt.show()

!pip install pandas

model.evaluate(X_test, y_test)