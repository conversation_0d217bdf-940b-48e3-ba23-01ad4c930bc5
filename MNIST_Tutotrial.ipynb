#CAMPUS X

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import Sequential
from tensorflow.keras.layers import <PERSON><PERSON>, <PERSON><PERSON>


(X_train, y_train), (X_test, y_test) = keras.datasets.mnist.load_data()
print(X_train.shape) #(60000, 28, 28)
print(X_test.shape) #(10000, 28, 28)


#print(X_train)
#print(X_test)
#print(X_train[0].shape)
#0print(X_test[0].shape)
y_train

import matplotlib.pyplot as plt
plt.imshow(X_train[4], cmap="binary")


X_train #pixel array of image


X_train = X_train/255 #To normalize the pixel value between (0 and 1)
X_test = X_test/255 #To normalize the pixel value
X_train

model = Sequential()
model.add(Flatten(input_shape=(28,28))) #To flaten the data. It converts the 2D data into 1D data
model.add(Dense(128, activation='relu')) #hidden nodes 128 and activation function is relu
model.add(Dense(10, activation='softmax')) #output nodes 10 and activation function is softmax
model.summary()

model.compile(loss='sparse_categorical_crossentropy', optimizer='Adam', metrics=['accuracy'])

model.fit(X_train, y_train, epochs=20, validation_split=0.2)

y_prob = model.predict(X_test) #stored the predicted value in a variable y_prob
model.predict(X_test) #stored the predicted value in a variable y_prob


y_prob.argmax(axis=1) #To get the maximum value from the array


from sklearn.metrics import accuracy_score
accuracy_score(y_test, y_pred)